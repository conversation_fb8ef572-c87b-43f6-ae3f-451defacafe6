import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import * as elevenlabsQueries from '@/utils/elevenlabs/queries';

/**
 * Create an ElevenLabs agent and store its ID in the external_provider_id field
 * 
 * This endpoint:
 * 1. Creates an agent in ElevenLabs
 * 2. Creates an agent in the database
 * 3. Creates an agent_config with the ElevenLabs agent ID in the external_provider_id field
 */
export async function POST(request: Request) {
  try {
    // Get the request body
    const { name, orgId, teamId, budget, voiceId, description, conversation_config } = await request.json();
    
    if (!name || !orgId) {
      return NextResponse.json(
        { error: 'Missing required parameters: name and orgId are required' },
        { status: 400 }
      );
    }
    
    // Format the name for ElevenLabs
    const formattedName = `${name} [SB-${orgId}]`;
    
    // Create the agent in ElevenLabs
    try {
      // Create the ElevenLabs agent
      const elevenLabsAgent = await elevenlabsQueries.createAgent(
        formattedName,
        conversation_config || {}
      );
      
      // Create Supabase client
      const supabase = await createClient();
      
      // Create the agent in the database
      const agentData: any = {
        name,
        organization_id: orgId,
      };
      
      // Add team_id if provided and not 'none'
      if (teamId && teamId !== 'none') {
        agentData.team_id = teamId;
      }
      
      // Add budget if provided (convert to cents)
      if (budget !== undefined) {
        agentData.budget_cents = Math.round(parseFloat(budget) * 100);
      }
      
      // Insert the agent
      const { data: agent, error: agentError } = await supabase
        .from('agents')
        .insert([agentData])
        .select()
        .single();
      
      if (agentError) {
        console.error('Error creating agent in database:', agentError);
        
        // Try to delete the ElevenLabs agent to avoid orphaned resources
        try {
          await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
        } catch (deleteError) {
          console.error('Error deleting orphaned ElevenLabs agent:', deleteError);
        }
        
        return NextResponse.json(
          { error: 'Failed to create agent in database', details: agentError.message },
          { status: 500 }
        );
      }
      
      // Create the internal agent config
      const internalConfigData = {
        agent_id: agent.id,
        config_type: 'internal',
        config: {
          language: 'en',
          prompt_config: conversation_config?.agent?.prompt || {
            prompt: "You are a helpful assistant.",
            llm: "gemini-2.0-flash-001",
            tools: [],
            knowledge_base: [],
            temperature: 0.5,
            max_tokens: -1
          },
          tts_config: conversation_config?.tts || {
            model_id: voiceId || "eleven_flash_v2",
            agent_output_audio_format: "pcm_16000",
            optimize_streaming_latency: 3,
            stability: 0.5,
            similarity_boost: 0.8
          }
        }
      };

      // Create the ElevenLabs config with the agent ID
      const elevenLabsConfigData = {
        agent_id: agent.id,
        config_type: 'elevenlabs',
        external_provider_id: elevenLabsAgent.agent_id,
        config: conversation_config?.tts || {}
      };
      
      // Insert both agent configs
      const { error: configError } = await supabase
        .from('agent_configs')
        .insert([internalConfigData, elevenLabsConfigData]);
      
      if (configError) {
        console.error('Error creating agent config:', configError);
        
        // Try to delete the agent and ElevenLabs agent to avoid orphaned resources
        try {
          await supabase.from('agents').delete().eq('id', agent.id);
          await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
        } catch (deleteError) {
          console.error('Error cleaning up after failed config creation:', deleteError);
        }
        
        return NextResponse.json(
          { error: 'Failed to create agent config', details: configError.message },
          { status: 500 }
        );
      }
      
      // Return success response
      return NextResponse.json({
        success: true,
        agent: {
          id: agent.id,
          name: agent.name,
          elevenlabs_agent_id: elevenLabsAgent.agent_id
        }
      });
    } catch (elevenLabsError) {
      console.error('Error creating ElevenLabs agent:', elevenLabsError);
      
      return NextResponse.json(
        { 
          error: 'Failed to create ElevenLabs agent',
          details: elevenLabsError instanceof Error ? elevenLabsError.message : String(elevenLabsError)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in create-agent endpoint:', error);
    
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
