'use server'
import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'
import * as supabaseQueries from '@/utils/supabase/queries'
import * as elevenlabsQueries from '@/utils/elevenlabs/queries'
import { tryCatch } from '@/utils/try-catch'
import {z} from 'zod';
import * as elevenLabsSync from '@/utils/sync/elevenlabs-sync';


export async function fetchAgents() {
  const supabase = await createClient()
  const { data, error } = await supabase
    .from('agents')
    .select('*')

  if (error) {
    throw new Error(error.message)
  }

  return data
}



type State = {
  success: boolean
  id?: string
  error?: string
}

const AGENT_TEMPLATES = supabaseQueries.getTemplates();


export async function createAgent(prevState: State, formData: FormData): Promise<State> {
  const agentSchema = z.object(
    {
      name: z.string().min(1, { message: 'Name is required' }),
      template: z.string().min(1, { message: 'Template is required' }),
      orgId: z.string().min(1, { message: 'Organization ID is required' }),
      teamId: z.string().optional(),
      budget: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
    }
  )
  const formValues = {
    name: formData.get('name')?.toString() ?? '',
    template: formData.get('template')?.toString() ?? '',
    orgId: formData.get('orgId')?.toString() ?? '',
    teamId: formData.get('teamId')?.toString() ?? undefined,
    budget: formData.get('budget')?.toString() ?? undefined,
  };



    const [error, agent] = await tryCatch(()=>agentSchema.parse(formValues))
    if (error) {
      return { success: false, error: error.message }
    }

    const name = agent.name;
    const template = agent.template
    const orgId = agent.orgId
    // If teamId is 'none', set it to undefined so it will be treated as null in the database
    const teamId = agent.teamId === 'none' ? undefined : agent.teamId
    const conversation_config = AGENT_TEMPLATES.find(t => t.id === template)?.conversation_config

    try {
      const supabase = await createClient();

      // First create the ElevenLabs agent
      const formattedName = `${name} [SB-${orgId}]`;
      const [elevenlabsError, elevenLabsAgent] = await tryCatch(() =>
        elevenlabsQueries.createAgent(formattedName, conversation_config)
      );

      if (elevenlabsError) {
        console.error('Error creating ElevenLabs agent:', elevenlabsError);
        return { success: false, error: elevenlabsError.message };
      }

      // Then create the agent in Supabase with the ElevenLabs agent ID
      const [supabaseError, supabaseAgent] = await tryCatch(() =>
        supabaseQueries.createAgent(
          supabase,
          name,
          orgId,
          conversation_config, // Pass the original conversation_config
          teamId,
          agent.budget,
          elevenLabsAgent.agent_id // Pass the ElevenLabs agent ID
        )
      );

      if (supabaseError) {
        // If Supabase creation fails, delete the ElevenLabs agent to avoid orphaned resources
        try {
          await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
        } catch (deleteError) {
          console.error('Error deleting orphaned ElevenLabs agent:', deleteError);
        }

        return { success: false, error: supabaseError.message };
      }

      return { success: true, id: supabaseAgent.id };
    } catch (error) {
      console.error('Error in createAgent:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }

}
export async function updateAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;
  const teamId = formData.get('teamId') as string;
  const budget = formData.get('budget') as string;
  const widgetSettings = formData.get('widgetSettings') as string;

  // Voice settings
  const voice = formData.get('voice') as string;
  const voiceLanguage = formData.get('voiceLanguage') as string;
  const latencyOptimization = formData.get('latencyOptimization') as string;
  const stability = formData.get('stability') as string;
  const speed = formData.get('speed') as string;
  const similarity = formData.get('similarity') as string;

  // Conversation settings (from general tab)
  const firstMessage = formData.get('firstMessage') as string;
  const prompt = formData.get('prompt') as string;
  const language = formData.get('language') as string;
  const llmProvider = formData.get('llmProvider') as string;
  const temperature = formData.get('temperature') as string;
  const tokenLimit = formData.get('tokenLimit') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Prepare update data for agent table
    const updateData: any = {};

    // Handle team assignment
    if (teamId) {
      // If teamId is 'none', set to null, otherwise use the provided ID
      updateData.team_id = teamId === 'none' ? null : teamId;
    }

    // Handle budget update
    if (budget) {
      // Convert dollars to cents
      updateData.budget_cents = Math.round(parseFloat(budget) * 100);
    }

    // Update agent table if needed
    if (Object.keys(updateData).length > 0) {
      const { data, error } = await supabase
        .from('agents')
        .update(updateData)
        .eq('id', agentId)
        .select()
        .single();

      if (error) {
        console.error('Error updating agent:', error);
        return { success: false, error: error.message };
      }
    }

    // Handle widget settings update if provided
    if (widgetSettings) {
      try {
        const widgetSettingsObj = JSON.parse(widgetSettings);

        // Get the current agent config
        const { data: agentConfig, error: configError } = await supabase
          .from('agent_configs')
          .select('*')
          .eq('agent_id', agentId)
          .eq('config_type', 'elevenlabs')
          .maybeSingle();

        if (configError) {
          console.error('Error fetching agent config:', configError);
          return { success: false, error: configError.message };
        }

        // If config exists, update it; otherwise create a new one
        if (agentConfig) {
          // Merge the existing custom_metadata with the new widget settings
          const currentMetadata = agentConfig.custom_metadata ?
            (agentConfig.custom_metadata as Record<string, any>) : {};
          const updatedMetadata = {
            ...currentMetadata,
            ...widgetSettingsObj
          };

          const { error: updateError } = await supabase
            .from('agent_configs')
            .update({ custom_metadata: updatedMetadata })
            .eq('id', agentConfig.id);

          if (updateError) {
            console.error('Error updating agent config:', updateError);
            return { success: false, error: updateError.message };
          }
        } else {
          // Create a new config record
          const { error: insertError } = await supabase
            .from('agent_configs')
            .insert({
              agent_id: agentId,
              config_type: 'internal',
              custom_metadata: widgetSettingsObj
            });

          if (insertError) {
            console.error('Error creating agent config:', insertError);
            return { success: false, error: insertError.message };
          }
        }
      } catch (parseError) {
        console.error('Error parsing widget settings:', parseError);
        return {
          success: false,
          error: parseError instanceof Error ? parseError.message : 'Invalid widget settings format'
        };
      }
    }

    // Handle voice settings update if any voice-related fields are provided
    const hasVoiceSettings = voice || voiceLanguage || latencyOptimization || stability ||
                            speed || similarity || firstMessage || prompt || language;

    if (hasVoiceSettings) {
      try {
        // Get both internal and ElevenLabs configs
        const { data: internalConfig } = await supabase
          .from('agent_configs')
          .select('*')
          .eq('agent_id', agentId)
          .eq('config_type', 'internal')
          .maybeSingle();

        const { data: elevenLabsConfig } = await supabase
          .from('agent_configs')
          .select('*')
          .eq('agent_id', agentId)
          .eq('config_type', 'elevenlabs')
          .maybeSingle();

        // Prepare updated configs
        const currentInternalConfig = (internalConfig?.config as any) || {};
        const currentElevenLabsConfig = (elevenLabsConfig?.config as any) || {};

        // Update internal config with conversation settings
        const updatedInternalConfig = {
          ...currentInternalConfig,
          language: language || currentInternalConfig.language,
          first_message: firstMessage || currentInternalConfig.first_message,
          prompt_config: {
            ...currentInternalConfig.prompt_config,
            prompt: prompt || currentInternalConfig.prompt_config?.prompt
          },
          tts_config: {
            ...currentInternalConfig.tts_config,
            model_id: voice || currentInternalConfig.tts_config?.model_id,
            optimize_streaming_latency: latencyOptimization ?
              parseFloat(latencyOptimization) : currentInternalConfig.tts_config?.optimize_streaming_latency,
            stability: stability ? parseFloat(stability) : currentInternalConfig.tts_config?.stability,
            similarity_boost: similarity ? parseFloat(similarity) : currentInternalConfig.tts_config?.similarity_boost
          }
        };

        // Update ElevenLabs config with voice settings
        const updatedElevenLabsConfig = {
          ...currentElevenLabsConfig,
          tts_config: {
            ...currentElevenLabsConfig.tts_config,
            model_id: voice || currentElevenLabsConfig.tts_config?.model_id,
            optimize_streaming_latency: latencyOptimization ?
              parseFloat(latencyOptimization) : currentElevenLabsConfig.tts_config?.optimize_streaming_latency,
            stability: stability ? parseFloat(stability) : currentElevenLabsConfig.tts_config?.stability,
            similarity_boost: similarity ? parseFloat(similarity) : currentElevenLabsConfig.tts_config?.similarity_boost
          }
        };

        // Update internal config
        if (internalConfig) {
          await supabase
            .from('agent_configs')
            .update({ config: updatedInternalConfig })
            .eq('id', internalConfig.id);
        }

        // Update ElevenLabs config
        if (elevenLabsConfig) {
          await supabase
            .from('agent_configs')
            .update({ config: updatedElevenLabsConfig })
            .eq('id', elevenLabsConfig.id);

          // Sync with ElevenLabs if there's an external agent ID
          if (elevenLabsConfig.external_provider_id) {
            try {
              // Get the agent name for ElevenLabs update
              const { data: agentData } = await supabase
                .from('agents')
                .select('name')
                .eq('id', agentId)
                .single();

              const agentName = agentData?.name || `Agent ${agentId}`;
              const formattedName = `${agentName} [SB-${orgId}]`;

              // Update the ElevenLabs agent with the new conversation config
              const conversationConfig = {
                agent: {
                  language: updatedInternalConfig.language,
                  prompt: updatedInternalConfig.prompt_config,
                  first_message: updatedInternalConfig.first_message
                },
                tts: updatedElevenLabsConfig.tts_config
              };

              await elevenlabsQueries.updateAgent(
                elevenLabsConfig.external_provider_id,
                formattedName,
                conversationConfig
              );
            } catch (syncError) {
              console.error('Error syncing with ElevenLabs:', syncError);
              // Don't fail the operation if ElevenLabs sync fails
            }
          }
        }
      } catch (voiceError) {
        console.error('Error updating voice settings:', voiceError);
        return {
          success: false,
          error: voiceError instanceof Error ? voiceError.message : 'Failed to update voice settings'
        };
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);
    revalidatePath(`/dashboard/${orgId}/agents/${agentId}`);

    return { success: true, id: agentId };
  } catch (error) {
    console.error('Error in updateAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
/**
 * Delete an agent and its associated resources
 */
export async function deleteAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Get agent details to check for ElevenLabs agent ID
    // Look for a  config with config_type 'elevenlabs'
    const { data: agentDetails } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId)
      .eq('config_type', 'elevenlabs')
      .maybeSingle();

    // Check if there's an ElevenLabs agent ID in the external_provider_id field
    let elevenLabsAgentId: string | undefined;

    if (agentDetails) {
      // Use type assertion to access the fields
      const agentConfig = agentDetails as any;

      // Check in external_provider_id field
      if (agentConfig.external_provider_id) {
        elevenLabsAgentId = agentConfig.external_provider_id;
      }
    }

    // Delete the agent from Supabase
    const [deleteError, success] = await tryCatch(() =>
      supabaseQueries.deleteAgent(supabase, agentId)
    );

    if (deleteError) {
      console.error('Error deleting agent:', deleteError);
      return { success: false, error: deleteError.message };
    }

    // If there's an ElevenLabs agent ID, delete it from ElevenLabs too
    if (elevenLabsAgentId) {
      const syncResult = await elevenLabsSync.deleteElevenLabsAgent(elevenLabsAgentId);

      if (!syncResult.elevenLabsSuccess) {
        console.error('Error deleting ElevenLabs agent:', syncResult.elevenLabsError);
        // Log the error but don't fail the operation since the Supabase deletion was successful
        // Consider adding this to a "sync repair queue" for later reconciliation
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);

    return { success: true };
  } catch (error) {
    console.error('Error in deleteAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function duplicateAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;
  const newName = formData.get('newName') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Duplicate the agent in Supabase
    const [duplicateError, newAgent] = await tryCatch(() =>
      supabaseQueries.duplicateAgent(supabase, agentId, newName)
    );

    if (duplicateError || !newAgent) {
      console.error('Error duplicating agent:', duplicateError);
      return {
        success: false,
        error: duplicateError?.message || 'Failed to duplicate agent'
      };
    }

    // Get the internal config for the agent
    const { data: internalConfig, error: internalConfigError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', newAgent.id)
      .eq('config_type', 'internal')
      .maybeSingle();

    // If there's an internal config, create a new ElevenLabs agent
    if (internalConfig && !internalConfigError) {
      // Get the config from the agent config
      // Use type assertion to handle the database schema mismatch
      const conversationConfig = (internalConfig as any).config || {};

      // Create a new ElevenLabs agent with the same configuration
      try {
        // First create the ElevenLabs agent
        const formattedName = `${newAgent.name || `Agent ${newAgent.id}`} [SB-${orgId}]`;
        const [elevenlabsError, elevenLabsAgent] = await tryCatch(() =>
          elevenlabsQueries.createAgent(formattedName, conversationConfig)
        );

        if (elevenlabsError) {
          console.error('Error creating ElevenLabs agent:', elevenlabsError);
          // Continue even if ElevenLabs creation fails
        } else {
          // Find the voice config for ElevenLabs
          const { data: voiceConfig } = await supabase
            .from('agent_configs')
            .select('*')
            .eq('agent_id', newAgent.id)
            .eq('config_type', 'elevenlabs')
            .maybeSingle();

          if (voiceConfig) {
            // Update the voice config with the new ElevenLabs agent ID
            await supabase
              .from('agent_configs')
              .update({ external_provider_id: elevenLabsAgent.agent_id } as any)
              .eq('id', voiceConfig.id);
          } else {
            // Create a new voice config for ElevenLabs
            await supabase
              .from('agent_configs')
              .insert({
                agent_id: newAgent.id,
                config_type: 'elevenlabs',
                external_provider_id: elevenLabsAgent.agent_id,
                config: conversationConfig || {}
              } as any);
          }
        }
      } catch (error) {
        console.error('Error in ElevenLabs agent creation:', error);
        // Continue even if ElevenLabs creation fails
      }

      // The ElevenLabs agent creation is handled in the try/catch block above
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);

    return { success: true, id: newAgent.id };
  } catch (error) {
    console.error('Error in duplicateAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
