'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useActionState } from 'react'

import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'

import { AgentGeneralTab } from './tabs/general-tab'
import { AgentVoiceTab } from './tabs/voice-tab'
import { AgentWidgetTab } from './tabs/widget-tab'

import { updateAgent } from '@/actions/serverActions'
import { getAgentDetails } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/client'
import { Database } from '@/types_db'

type AgentConfig = Database['public']['Tables']['agent_configs']['Row']

import { DeleteAgentButton } from '../delete-agent-button'
import { DuplicateAgentButton } from '../duplicate-agent-button'

import { WidgetProvider, defaultWidgetSettings, WidgetSettings } from '@/contexts/widget-context'

// Props
interface AgentSettingsSidebarProps {
  agentId: string
  orgId: string
  agentData?: any
  elevenLabsConfig?: AgentConfig
  internalConfig?: AgentConfig
}

// Fetch combined agent data and config
async function fetchAgentData(agentId: string) {
  const supabase = createClient()
  const agentDetails = await getAgentDetails(supabase, agentId)
  const { data: agentConfig } = await supabase
    .from('agent_configs')
    .select('*')
    .eq('agent_id', agentId)
    .single()

  return {
    ...agentDetails,
    config: agentConfig || {}
  }
}

export function AgentSettingsSidebar({ agentId, orgId, agentData: serverAgentData, elevenLabsConfig: serverElevenLabsConfig, internalConfig: serverInternalConfig }: AgentSettingsSidebarProps) {
  const [isDirty, setIsDirty] = useState(false)
  const [initialWidgetSettings, setInitialWidgetSettings] = useState<WidgetSettings>(defaultWidgetSettings)

  // useActionState for server form submission
  const initialState = { success: false, error: '', id: '' }
  const [formState, formAction, isPending] = useActionState(updateAgent, initialState)

  // Reset dirty state on successful save
  useEffect(() => {
    if (formState.success) setIsDirty(false)
  }, [formState.success])

  // Use server-provided agent data or fallback to client-side fetch
  const { data: clientAgentData } = useQuery({
    queryKey: ['agent-details', agentId],
    queryFn: () => fetchAgentData(agentId),
    enabled: !!agentId && !serverAgentData,
  })

  const agentData = serverAgentData || clientAgentData

  // Use server-provided config or fallback to client-side fetch for internal config
  const { data: clientInternalConfig } = useQuery({
    queryKey: ['agent-internal-config', agentId],
    queryFn: async () => {
      if (!agentId) return null
      const supabase = createClient()
      const { data } = await supabase
        .from('agent_configs')
        .select('*')
        .eq('agent_id', agentId)
        .eq('config_type', 'internal')
        .single()
      return data as AgentConfig | null
    },
    enabled: !!agentId && !serverInternalConfig,
  })

  // Fetch ElevenLabs config
  const { data: elevenLabsConfig } = useQuery({
    queryKey: ['agent-elevenlabs-config', agentId],
    queryFn: async () => {
      if (!agentId) return null
      const supabase = createClient()
      const { data } = await supabase
        .from('agent_configs')
        .select('*')
        .eq('agent_id', agentId)
        .eq('config_type', 'elevenlabs')
        .maybeSingle()
      return data as AgentConfig | null
    },
    enabled: !!agentId && !serverElevenLabsConfig,
  })

  const internalConfig = serverInternalConfig || clientInternalConfig
  const elevenLabsConfigData = serverElevenLabsConfig || elevenLabsConfig

  // Safely get config metadata
  const getMetadataValue = (config: AgentConfig | null | undefined, key: string, defaultValue: any): any => {
    if (!config || !config.custom_metadata) return defaultValue
    const metadata = config.custom_metadata as Record<string, any>
    return metadata[key] !== undefined ? metadata[key] : defaultValue
  }

  // Helper to get config values from the config JSONB field
  const getConfigValue = (config: AgentConfig | null | undefined, path: string, defaultValue: any): any => {
    if (!config || !config.config) return defaultValue
    const configData = config.config as Record<string, any>
    const keys = path.split('.')
    let value = configData
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return defaultValue
      }
    }
    return value !== undefined ? value : defaultValue
  }

  // Voice settings - combine from both internal and ElevenLabs configs
  const voiceSettings = {
    // Voice ID from ElevenLabs config or internal config
    voice: getConfigValue(elevenLabsConfigData, 'tts_config.model_id', '') ||
           getConfigValue(internalConfig, 'tts_config.model_id', ''),

    // Voice language - from voice config or agent language
    voiceLanguage: getConfigValue(elevenLabsConfigData, 'tts_config.voice_language', '') ||
                   getConfigValue(internalConfig, 'tts_config.voice_language', '') ||
                   getConfigValue(internalConfig, 'language', 'en'),

    // Voice parameters from ElevenLabs config or internal config
    latencyOptimization: getConfigValue(elevenLabsConfigData, 'tts_config.optimize_streaming_latency', 3) ||
                        getConfigValue(internalConfig, 'tts_config.optimize_streaming_latency', 3),
    stability: getConfigValue(elevenLabsConfigData, 'tts_config.stability', 0.5) ||
               getConfigValue(internalConfig, 'tts_config.stability', 0.5),
    speed: 1.0, // ElevenLabs doesn't have speed, this might be a UI-only setting
    similarity: getConfigValue(elevenLabsConfigData, 'tts_config.similarity_boost', 0.75) ||
                getConfigValue(internalConfig, 'tts_config.similarity_boost', 0.75),

    // Conversation settings from internal config
    firstMessage: getConfigValue(internalConfig, 'first_message', ''),
    prompt: getConfigValue(internalConfig, 'prompt_config.prompt', ''),
    language: getConfigValue(internalConfig, 'language', 'en'),
  }

  // Widget settings - use internal config for widget metadata
  useEffect(() => {
    if (internalConfig?.custom_metadata) {
      try {
        const metadata = internalConfig.custom_metadata as Record<string, any>
        if (metadata.widget) {
          setInitialWidgetSettings(prev => ({
            ...prev,
            position: metadata.widget.position ?? prev.position,
            customText: metadata.widget.customText ?? prev.customText,
            hideIcon: metadata.widget.hideIcon ?? prev.hideIcon,
            customIcon: metadata.widget.customIcon ?? prev.customIcon,
            customCSS: metadata.widget.customCSS ?? prev.customCSS,
            widgetType: metadata.widget.widgetType ?? prev.widgetType,
          }))
        }
      } catch (error) {
        console.error('Error parsing widget settings:', error)
      }
    }
  }, [internalConfig])

  return (
    <div className="h-full">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">{agentData?.name || 'Agent Settings'}</h2>
          <div className="flex gap-2">
            <DuplicateAgentButton
              agentId={agentId}
              orgId={orgId}
              agentName={agentData?.name || 'Agent'}
              size="sm"
              variant="outline"
            />
            <DeleteAgentButton
              agentId={agentId}
              orgId={orgId}
              agentName={agentData?.name || 'Agent'}
              size="sm"
            />
          </div>
        </div>
        <p className="text-sm text-muted-foreground mb-4">Configure your agent settings</p>
      </div>

      <ScrollArea className="h-[calc(100vh-120px)]">
        <form action={formAction} className="px-4 pb-16">
          <input type="hidden" name="agentId" value={agentId} />
          <input type="hidden" name="orgId" value={orgId} />

          <Tabs defaultValue="agent" className="w-full">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="agent" className="flex-1">Agent</TabsTrigger>
              <TabsTrigger value="voice" className="flex-1">Voice</TabsTrigger>
              <TabsTrigger value="widget" className="flex-1">Widget</TabsTrigger>
            </TabsList>

            <TabsContent value="agent" className='focus:ring-0'>
              <AgentGeneralTab
                setIsDirty={setIsDirty}
                initialValues={{
                  name: agentData?.name,
                  teamId: agentData?.team_id,
                  budget_cents: agentData?.budget_cents,
                  defaultLanguage: voiceSettings.language,
                  firstMessage: voiceSettings.firstMessage,
                  systemPrompt: voiceSettings.prompt,
                  llmProvider: getConfigValue(internalConfig, 'prompt_config.llm', 'gpt-4'),
                  temperature: getConfigValue(internalConfig, 'prompt_config.temperature', 0.7),
                  tokenLimit: getConfigValue(internalConfig, 'prompt_config.max_tokens', -1),
                }}
              />
            </TabsContent>

            <TabsContent value="voice" className='focus:ring-0'>
              <AgentVoiceTab
                setIsDirty={setIsDirty}
                initialValues={{
                  voice: voiceSettings.voice,
                  voiceLanguage: voiceSettings.voiceLanguage || 'en',
                  latencyOptimization: voiceSettings.latencyOptimization,
                  stability: voiceSettings.stability,
                  speed: voiceSettings.speed,
                  similarity: voiceSettings.similarity
                }}
              />
            </TabsContent>

            <TabsContent value="widget" className='focus:ring-0'>
              <WidgetProvider initialSettings={initialWidgetSettings}>
                <AgentWidgetTab setIsDirty={setIsDirty} agentId={agentId} />
              </WidgetProvider>
            </TabsContent>
          </Tabs>

          {isDirty && (
            <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 flex items-center justify-between z-10">
              <span>You have unsaved changes</span>
              <div className="space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDirty(false)}>
                  Clear
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          )}

          {formState.error && (
            <p className="text-red-500 mt-2">{formState.error}</p>
          )}
          {formState.success && (
            <p className="text-green-600 mt-2">Saved successfully!</p>
          )}
        </form>
      </ScrollArea>
    </div>
  )
}
