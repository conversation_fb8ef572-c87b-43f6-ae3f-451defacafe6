

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE TYPE "public"."pricing_plan_interval" AS ENUM (
    'day',
    'week',
    'month',
    'year'
);


ALTER TYPE "public"."pricing_plan_interval" OWNER TO "postgres";


CREATE TYPE "public"."pricing_type" AS ENUM (
    'one_time',
    'recurring'
);


ALTER TYPE "public"."pricing_type" OWNER TO "postgres";


CREATE TYPE "public"."subscription_status" AS ENUM (
    'trialing',
    'active',
    'canceled',
    'incomplete',
    'incomplete_expired',
    'past_due',
    'unpaid',
    'paused'
);


ALTER TYPE "public"."subscription_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_start_call"("agent" "uuid", "estimated_cost" integer) RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  org_id UUID;
  team_id UUID;
  org_balance INT;
  agent_daily INT;
  agent_limit INT;
  team_daily INT;
  team_limit INT;
BEGIN
  SELECT organization_id, team_id INTO org_id, team_id FROM agents WHERE id = agent;

  SELECT balance_cents INTO org_balance FROM credit_wallets WHERE organization_id = org_id;
  IF org_balance < estimated_cost THEN RETURN FALSE; END IF;

  SELECT daily_limit_cents INTO agent_limit FROM agent_budget_limits WHERE agent_id = agent;
  SELECT COALESCE(SUM(-amount_cents), 0) INTO agent_daily
  FROM credit_transactions WHERE agent_id = agent AND created_at >= date_trunc('day', now());
  IF agent_limit IS NOT NULL AND agent_daily + estimated_cost > agent_limit THEN RETURN FALSE; END IF;

  SELECT daily_limit_cents INTO team_limit FROM team_budget_limits WHERE team_id = team_id;
  SELECT COALESCE(SUM(-amount_cents), 0) INTO team_daily
  FROM credit_transactions WHERE team_id = team_id AND created_at >= date_trunc('day', now());
  IF team_limit IS NOT NULL AND team_daily + estimated_cost > team_limit THEN RETURN FALSE; END IF;

  RETURN TRUE;
END;
$$;


ALTER FUNCTION "public"."can_start_call"("agent" "uuid", "estimated_cost" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."charge_live_sessions"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  s RECORD;
  now_ts TIMESTAMP := NOW();
  elapsed_secs INT;
  charge INT;
  wallet_balance INT;
BEGIN
  FOR s IN SELECT * FROM live_sessions WHERE is_active LOOP
    elapsed_secs := EXTRACT(EPOCH FROM (now_ts - s.last_charged_at));
    charge := elapsed_secs * s.per_second_cost_cents;

    SELECT balance_cents INTO wallet_balance FROM credit_wallets WHERE organization_id = s.organization_id;

    IF wallet_balance < charge THEN
      UPDATE live_sessions
      SET is_active = FALSE,
          ended_at = now_ts,
          ended_reason = 'insufficient_credits'
      WHERE id = s.id;
      CONTINUE;
    END IF;

    -- Deduct
    UPDATE credit_wallets SET balance_cents = balance_cents - charge WHERE organization_id = s.organization_id;

    INSERT INTO credit_transactions (
      organization_id, agent_id, team_id, conversation_id,
      amount_cents, type, description, duration_seconds
    ) VALUES (
      s.organization_id, s.agent_id, s.team_id, NULL, -charge, 'usage', 'Periodic live session charge', elapsed_secs
    );

    UPDATE live_sessions
    SET last_charged_at = now_ts,
        total_duration_secs = total_duration_secs + elapsed_secs,
        total_cost_cents = total_cost_cents + charge
    WHERE id = s.id;
  END LOOP;
END;
$$;


ALTER FUNCTION "public"."charge_live_sessions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_organization"("org_name" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_org_id uuid := gen_random_uuid();
BEGIN
  -- Create the organization
  INSERT INTO public.organizations (id, name)
  VALUES (new_org_id, org_name);
  
  -- Add the current user as an admin
  INSERT INTO public.organization_memberships (organization_id, user_id, role)
  VALUES (new_org_id, auth.uid(), 'admin');
  
  -- Create a wallet for the organization if needed
  INSERT INTO public.credit_wallets (organization_id, balance_cents)
  VALUES (new_org_id, 0);
  
  RETURN new_org_id;
END;
$$;


ALTER FUNCTION "public"."create_organization"("org_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_user_with_org"("user_id" "uuid", "email" "text", "full_name" "text", "avatar_url" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
declare
  new_org_id uuid := gen_random_uuid();
begin
  insert into public.users (id, email, full_name, avatar_url)
  values (user_id, email, full_name, avatar_url);

  insert into public.organizations (id, name)
  values (new_org_id, concat('Org of ', email));

  insert into public.organization_memberships (organization_id, user_id, role)
  values (new_org_id, user_id, 'admin');

  insert into public.credit_wallets (organization_id, balance_cents)
  values (new_org_id, 0);
end;
$$;


ALTER FUNCTION "public"."create_user_with_org"("user_id" "uuid", "email" "text", "full_name" "text", "avatar_url" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."finalize_call"("agent" "uuid", "cost" integer, "conversation" "uuid", "duration_secs" integer DEFAULT NULL::integer) RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  org_id UUID;
  team_id UUID;
  org_balance INT;
BEGIN
  SELECT organization_id, team_id INTO org_id, team_id FROM agents WHERE id = agent;
  SELECT balance_cents INTO org_balance FROM credit_wallets WHERE organization_id = org_id;
  IF org_balance < cost THEN RETURN FALSE; END IF;

  UPDATE credit_wallets SET balance_cents = balance_cents - cost WHERE organization_id = org_id;

  INSERT INTO credit_transactions (
    organization_id, agent_id, team_id, conversation_id,
    amount_cents, type, description, duration_seconds
  ) VALUES (
    org_id, agent, team_id, conversation, -cost, 'usage', 'Post-call deduction', duration_secs
  );

  RETURN TRUE;
END;
$$;


ALTER FUNCTION "public"."finalize_call"("agent" "uuid", "cost" integer, "conversation" "uuid", "duration_secs" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_elevenlabs_config"("config_id" "uuid") RETURNS "jsonb"
    LANGUAGE "sql" STABLE
    AS $$
  SELECT jsonb_build_object(
    'name', agents.name,
    'conversation_config', jsonb_build_object(
      'agent', jsonb_build_object(
        'language', ac.language,
        'prompt', ac.prompt_config,
        'first_message', ac.first_message,
        'dynamic_variables', ac.dynamic_variables
      ),
      'asr', ac.asr_config,
      'tts', ac.tts_config,
      'turn', ac.turn_config,
      'conversation', ac.conversation_config,
      'language_presets', '{}',
      'is_blocked_ivc', false,
      'is_blocked_non_ivc', false
    ),
    'platform_settings', ac.platform_settings
  )
  FROM agent_configs ac
  JOIN agents ON agents.id = ac.agent_id
  WHERE ac.id = config_id;
$$;


ALTER FUNCTION "public"."get_elevenlabs_config"("config_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$declare
  new_org_id uuid := gen_random_uuid();
begin
  -- Ensure email exists
  if new.email is null then
    raise exception 'Email is required to create user and org';
  end if;

  -- Insert user
  insert into public.users (id, email, full_name, avatar_url)
  values (
    new.id,
    new.email,
    new.raw_user_meta_data->>'full_name',
    new.raw_user_meta_data->>'avatar_url'
  );

  -- Create org
  insert into public.organizations (id, name)
  values (new_org_id, 'My Workspace');

  -- Add as admin
  insert into public.organization_memberships (organization_id, user_id, role)
  values (new_org_id, new.id, 'admin');

  -- Add wallet
  insert into public.credit_wallets (organization_id, balance_cents)
  values (new_org_id, 0);

  return new;
end;$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();


CREATE OR REPLACE FUNCTION "public"."insert_conversation_with_messages"("_id" "uuid", "_agent_id" "uuid", "_status" "text", "_start_time" timestamp without time zone, "_duration_secs" integer, "_cost_cents" integer, "_summary" "text", "_success_status" "text", "_transcript" "jsonb", "_metadata" "jsonb", "_analysis" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  msg JSONB;
  i INTEGER := 0;
BEGIN
  -- Insert into conversations
  INSERT INTO conversations (
    id, agent_id, status, start_time, duration_secs,
    cost_cents, summary, success_status,
    metadata, analysis
  ) VALUES (
    _id, _agent_id, _status, _start_time, _duration_secs,
    _cost_cents, _summary, _success_status,
    _metadata, _analysis
  );

  -- Loop through transcript and insert messages
  FOR i IN 0 .. jsonb_array_length(_transcript) - 1 LOOP
    msg := _transcript -> i;

    INSERT INTO conversation_messages (
      conversation_id, sender_type, message, meta
    ) VALUES (
      _id,
      COALESCE(msg ->> 'role', 'agent'),
      msg ->> 'message',
      jsonb_build_object(
        'tool_calls', msg -> 'tool_calls',
        'tool_results', msg -> 'tool_results',
        'feedback', msg -> 'feedback',
        'time_in_call_secs', msg -> 'time_in_call_secs',
        'metrics', msg -> 'conversation_turn_metrics'
      )
    );
  END LOOP;
END;
$$;


ALTER FUNCTION "public"."insert_conversation_with_messages"("_id" "uuid", "_agent_id" "uuid", "_status" "text", "_start_time" timestamp without time zone, "_duration_secs" integer, "_cost_cents" integer, "_summary" "text", "_success_status" "text", "_transcript" "jsonb", "_metadata" "jsonb", "_analysis" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_member"("org_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE
    AS $$
  SELECT EXISTS (
    SELECT 1 FROM organization_memberships
    WHERE organization_id = org_id AND user_id = auth.uid()
  );
$$;


ALTER FUNCTION "public"."is_member"("org_id" "uuid") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."agent_budget_limits" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "agent_id" "uuid",
    "daily_limit_cents" integer,
    "weekly_limit_cents" integer,
    "monthly_limit_cents" integer,
    "updated_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."agent_budget_limits" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."agent_configs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "agent_id" "uuid",
    "config_type" "text" NOT NULL,
    "provider" "text",
    "external_provider_id" "text",
    "config" "jsonb" DEFAULT '{"client_events": ["audio", "interruption", "user_transcript", "agent_response", "agent_response_correction"], "max_duration_seconds": 300}'::"jsonb",
    "custom_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "agent_configs_config_type_check" CHECK (("config_type" = ANY (ARRAY['external'::"text", 'internal'::"text", 'elevenlabs'::"text", 'text_widget'::"text", 'native'::"text", 'global'::"text", 'voice'::"text"])))
);


ALTER TABLE "public"."agent_configs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."agent_teams" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "name" "text",
    "budget_cents" integer DEFAULT 0,
    "created_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."agent_teams" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."agents" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "team_id" "uuid",
    "name" "text",
    "budget_cents" integer DEFAULT 0,
    "billing_strategy" "text" DEFAULT 'post_call'::"text",
    "per_second_cost_cents" integer DEFAULT 0,
    "created_at" timestamp without time zone DEFAULT "now"(),
    CONSTRAINT "agents_billing_strategy_check" CHECK (("billing_strategy" = ANY (ARRAY['post_call'::"text", 'periodic'::"text"])))
);


ALTER TABLE "public"."agents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversation_messages" (
    "id" integer NOT NULL,
    "conversation_id" "uuid" NOT NULL,
    "sender_type" "text" NOT NULL,
    "sender_id" "text",
    "message" "text" NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "meta" "jsonb",
    CONSTRAINT "conversation_messages_sender_type_check" CHECK (("sender_type" = ANY (ARRAY['user'::"text", 'agent'::"text", 'human'::"text", 'system'::"text"])))
);


ALTER TABLE "public"."conversation_messages" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."conversation_messages_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."conversation_messages_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."conversation_messages_id_seq" OWNED BY "public"."conversation_messages"."id";



CREATE TABLE IF NOT EXISTS "public"."conversations" (
    "id" "uuid" NOT NULL,
    "agent_id" "uuid" NOT NULL,
    "status" "text",
    "start_time" timestamp without time zone,
    "duration_secs" integer,
    "cost_cents" integer,
    "summary" "text",
    "success_status" "text",
    "metadata" "jsonb",
    "analysis" "jsonb",
    "created_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."credit_transactions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "agent_id" "uuid",
    "team_id" "uuid",
    "conversation_id" "uuid",
    "type" "text",
    "amount_cents" integer,
    "description" "text",
    "duration_seconds" integer,
    "created_at" timestamp without time zone DEFAULT "now"(),
    CONSTRAINT "credit_transactions_type_check" CHECK (("type" = ANY (ARRAY['usage'::"text", 'top_up'::"text", 'bonus'::"text", 'refund'::"text", 'adjustment'::"text"])))
);


ALTER TABLE "public"."credit_transactions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."credit_wallets" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "balance_cents" integer DEFAULT 0,
    "updated_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."credit_wallets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."customers" (
    "id" "uuid" NOT NULL,
    "stripe_customer_id" "text"
);


ALTER TABLE "public"."customers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."live_sessions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "agent_id" "uuid",
    "team_id" "uuid",
    "started_at" timestamp without time zone DEFAULT "now"(),
    "last_charged_at" timestamp without time zone DEFAULT "now"(),
    "is_active" boolean DEFAULT true,
    "ended_at" timestamp without time zone,
    "ended_reason" "text",
    "per_second_cost_cents" integer NOT NULL,
    "total_duration_secs" integer DEFAULT 0,
    "total_cost_cents" integer DEFAULT 0
);


ALTER TABLE "public"."live_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_memberships" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "user_id" "uuid",
    "role" "text" DEFAULT 'member'::"text",
    "joined_at" timestamp without time zone DEFAULT "now"(),
    CONSTRAINT "organization_memberships_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'member'::"text"])))
);


ALTER TABLE "public"."organization_memberships" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."organizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prices" (
    "id" "text" NOT NULL,
    "product_id" "text",
    "active" boolean,
    "description" "text",
    "unit_amount" bigint,
    "currency" "text",
    "type" "public"."pricing_type",
    "interval" "public"."pricing_plan_interval",
    "interval_count" integer,
    "trial_period_days" integer,
    "metadata" "jsonb",
    CONSTRAINT "prices_currency_check" CHECK (("char_length"("currency") = 3))
);


ALTER TABLE "public"."prices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "text" NOT NULL,
    "active" boolean,
    "name" "text",
    "description" "text",
    "image" "text",
    "metadata" "jsonb"
);


ALTER TABLE "public"."products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscriptions" (
    "id" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "status" "public"."subscription_status",
    "metadata" "jsonb",
    "price_id" "text",
    "quantity" integer,
    "cancel_at_period_end" boolean,
    "created" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "current_period_start" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "current_period_end" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "ended_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "cancel_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "canceled_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "trial_start" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "trial_end" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."subscriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."team_budget_limits" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid",
    "daily_limit_cents" integer,
    "weekly_limit_cents" integer,
    "monthly_limit_cents" integer,
    "updated_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."team_budget_limits" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "full_name" "text",
    "avatar_url" "text",
    "billing_address" "jsonb",
    "payment_method" "jsonb",
    "email" "text" DEFAULT ''::"text" NOT NULL,
    "metadata" "jsonb",
    "created_at" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "updated_at" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."conversation_messages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."conversation_messages_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."agent_budget_limits"
    ADD CONSTRAINT "agent_budget_limits_agent_id_key" UNIQUE ("agent_id");



ALTER TABLE ONLY "public"."agent_budget_limits"
    ADD CONSTRAINT "agent_budget_limits_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."agent_configs"
    ADD CONSTRAINT "agent_configs_agent_id_key" UNIQUE ("agent_id");



ALTER TABLE ONLY "public"."agent_configs"
    ADD CONSTRAINT "agent_configs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."agent_teams"
    ADD CONSTRAINT "agent_teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."agents"
    ADD CONSTRAINT "agents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversation_messages"
    ADD CONSTRAINT "conversation_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."credit_transactions"
    ADD CONSTRAINT "credit_transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."credit_wallets"
    ADD CONSTRAINT "credit_wallets_organization_id_key" UNIQUE ("organization_id");



ALTER TABLE ONLY "public"."credit_wallets"
    ADD CONSTRAINT "credit_wallets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."live_sessions"
    ADD CONSTRAINT "live_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_memberships"
    ADD CONSTRAINT "organization_memberships_organization_id_user_id_key" UNIQUE ("organization_id", "user_id");



ALTER TABLE ONLY "public"."organization_memberships"
    ADD CONSTRAINT "organization_memberships_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prices"
    ADD CONSTRAINT "prices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_budget_limits"
    ADD CONSTRAINT "team_budget_limits_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_budget_limits"
    ADD CONSTRAINT "team_budget_limits_team_id_key" UNIQUE ("team_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_conversations_agent_id" ON "public"."conversations" USING "btree" ("agent_id");



CREATE INDEX "idx_conversations_start_time" ON "public"."conversations" USING "btree" ("start_time");



CREATE INDEX "idx_messages_convo_id" ON "public"."conversation_messages" USING "btree" ("conversation_id");



CREATE INDEX "idx_messages_created_at" ON "public"."conversation_messages" USING "btree" ("created_at");



ALTER TABLE ONLY "public"."agent_budget_limits"
    ADD CONSTRAINT "agent_budget_limits_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id");



ALTER TABLE ONLY "public"."agent_configs"
    ADD CONSTRAINT "agent_configs_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."agent_teams"
    ADD CONSTRAINT "agent_teams_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."agents"
    ADD CONSTRAINT "agents_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."agents"
    ADD CONSTRAINT "agents_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."agent_teams"("id");



ALTER TABLE ONLY "public"."conversation_messages"
    ADD CONSTRAINT "conversation_messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."credit_transactions"
    ADD CONSTRAINT "credit_transactions_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id");



ALTER TABLE ONLY "public"."credit_transactions"
    ADD CONSTRAINT "credit_transactions_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id");



ALTER TABLE ONLY "public"."credit_transactions"
    ADD CONSTRAINT "credit_transactions_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."credit_transactions"
    ADD CONSTRAINT "credit_transactions_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."agent_teams"("id");



ALTER TABLE ONLY "public"."credit_wallets"
    ADD CONSTRAINT "credit_wallets_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."live_sessions"
    ADD CONSTRAINT "live_sessions_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id");



ALTER TABLE ONLY "public"."live_sessions"
    ADD CONSTRAINT "live_sessions_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."live_sessions"
    ADD CONSTRAINT "live_sessions_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."agent_teams"("id");



ALTER TABLE ONLY "public"."organization_memberships"
    ADD CONSTRAINT "organization_memberships_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_memberships"
    ADD CONSTRAINT "organization_memberships_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prices"
    ADD CONSTRAINT "prices_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_price_id_fkey" FOREIGN KEY ("price_id") REFERENCES "public"."prices"("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."team_budget_limits"
    ADD CONSTRAINT "team_budget_limits_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."agent_teams"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



CREATE POLICY "Allow public read-only access." ON "public"."prices" FOR SELECT USING (true);



CREATE POLICY "Allow public read-only access." ON "public"."products" FOR SELECT USING (true);



CREATE POLICY "Can only view own subs data." ON "public"."subscriptions" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Can update own user data." ON "public"."users" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Can view own user data." ON "public"."users" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Members can insert agent limits" ON "public"."agent_budget_limits" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_budget_limits"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can insert agents" ON "public"."agents" WITH CHECK ("public"."is_member"("organization_id"));



CREATE POLICY "Members can insert configs" ON "public"."agent_configs" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_configs"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can insert sessions" ON "public"."live_sessions" WITH CHECK ("public"."is_member"("organization_id"));



CREATE POLICY "Members can insert team limits" ON "public"."team_budget_limits" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."agent_teams"
  WHERE (("agent_teams"."id" = "team_budget_limits"."team_id") AND "public"."is_member"("agent_teams"."organization_id")))));



CREATE POLICY "Members can insert transactions" ON "public"."credit_transactions" WITH CHECK ("public"."is_member"("organization_id"));



CREATE POLICY "Members can update agent limits" ON "public"."agent_budget_limits" USING ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_budget_limits"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can update agents" ON "public"."agents" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can update configs" ON "public"."agent_configs" USING ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_configs"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can update sessions" ON "public"."live_sessions" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can update team limits" ON "public"."team_budget_limits" USING ((EXISTS ( SELECT 1
   FROM "public"."agent_teams"
  WHERE (("agent_teams"."id" = "team_budget_limits"."team_id") AND "public"."is_member"("agent_teams"."organization_id")))));



CREATE POLICY "Members can view agent configs" ON "public"."agent_configs" USING ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_configs"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can view agent limits" ON "public"."agent_budget_limits" USING ((EXISTS ( SELECT 1
   FROM "public"."agents"
  WHERE (("agents"."id" = "agent_budget_limits"."agent_id") AND "public"."is_member"("agents"."organization_id")))));



CREATE POLICY "Members can view agents" ON "public"."agents" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can view organizations" ON "public"."organizations" USING ("public"."is_member"("id"));



CREATE POLICY "Members can view sessions" ON "public"."live_sessions" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can view team limits" ON "public"."team_budget_limits" USING ((EXISTS ( SELECT 1
   FROM "public"."agent_teams"
  WHERE (("agent_teams"."id" = "team_budget_limits"."team_id") AND "public"."is_member"("agent_teams"."organization_id")))));



CREATE POLICY "Members can view teams" ON "public"."agent_teams" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can view their memberships" ON "public"."organization_memberships" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Members can view transactions" ON "public"."credit_transactions" USING ("public"."is_member"("organization_id"));



CREATE POLICY "Members can view wallet" ON "public"."credit_wallets" USING ("public"."is_member"("organization_id"));



CREATE POLICY "User can access self" ON "public"."users" USING (("id" = "auth"."uid"()));



CREATE POLICY "User can insert self" ON "public"."users" WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "User can update self" ON "public"."users" USING (("id" = "auth"."uid"()));



ALTER TABLE "public"."agent_budget_limits" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."agent_configs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."agent_teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."agents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."credit_transactions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."credit_wallets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."customers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."live_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."organization_memberships" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prices" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."products" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscriptions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_budget_limits" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."can_start_call"("agent" "uuid", "estimated_cost" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."can_start_call"("agent" "uuid", "estimated_cost" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_start_call"("agent" "uuid", "estimated_cost" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."charge_live_sessions"() TO "anon";
GRANT ALL ON FUNCTION "public"."charge_live_sessions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."charge_live_sessions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_organization"("org_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_organization"("org_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_organization"("org_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_with_org"("user_id" "uuid", "email" "text", "full_name" "text", "avatar_url" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_with_org"("user_id" "uuid", "email" "text", "full_name" "text", "avatar_url" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_with_org"("user_id" "uuid", "email" "text", "full_name" "text", "avatar_url" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."finalize_call"("agent" "uuid", "cost" integer, "conversation" "uuid", "duration_secs" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."finalize_call"("agent" "uuid", "cost" integer, "conversation" "uuid", "duration_secs" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."finalize_call"("agent" "uuid", "cost" integer, "conversation" "uuid", "duration_secs" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("config_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("config_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("config_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_conversation_with_messages"("_id" "uuid", "_agent_id" "uuid", "_status" "text", "_start_time" timestamp without time zone, "_duration_secs" integer, "_cost_cents" integer, "_summary" "text", "_success_status" "text", "_transcript" "jsonb", "_metadata" "jsonb", "_analysis" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."insert_conversation_with_messages"("_id" "uuid", "_agent_id" "uuid", "_status" "text", "_start_time" timestamp without time zone, "_duration_secs" integer, "_cost_cents" integer, "_summary" "text", "_success_status" "text", "_transcript" "jsonb", "_metadata" "jsonb", "_analysis" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_conversation_with_messages"("_id" "uuid", "_agent_id" "uuid", "_status" "text", "_start_time" timestamp without time zone, "_duration_secs" integer, "_cost_cents" integer, "_summary" "text", "_success_status" "text", "_transcript" "jsonb", "_metadata" "jsonb", "_analysis" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_member"("org_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_member"("org_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_member"("org_id" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."agent_budget_limits" TO "anon";
GRANT ALL ON TABLE "public"."agent_budget_limits" TO "authenticated";
GRANT ALL ON TABLE "public"."agent_budget_limits" TO "service_role";



GRANT ALL ON TABLE "public"."agent_configs" TO "anon";
GRANT ALL ON TABLE "public"."agent_configs" TO "authenticated";
GRANT ALL ON TABLE "public"."agent_configs" TO "service_role";



GRANT ALL ON TABLE "public"."agent_teams" TO "anon";
GRANT ALL ON TABLE "public"."agent_teams" TO "authenticated";
GRANT ALL ON TABLE "public"."agent_teams" TO "service_role";



GRANT ALL ON TABLE "public"."agents" TO "anon";
GRANT ALL ON TABLE "public"."agents" TO "authenticated";
GRANT ALL ON TABLE "public"."agents" TO "service_role";



GRANT ALL ON TABLE "public"."conversation_messages" TO "anon";
GRANT ALL ON TABLE "public"."conversation_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."conversation_messages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."conversation_messages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."conversation_messages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."conversation_messages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."conversations" TO "anon";
GRANT ALL ON TABLE "public"."conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."conversations" TO "service_role";



GRANT ALL ON TABLE "public"."credit_transactions" TO "anon";
GRANT ALL ON TABLE "public"."credit_transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."credit_transactions" TO "service_role";



GRANT ALL ON TABLE "public"."credit_wallets" TO "anon";
GRANT ALL ON TABLE "public"."credit_wallets" TO "authenticated";
GRANT ALL ON TABLE "public"."credit_wallets" TO "service_role";



GRANT ALL ON TABLE "public"."customers" TO "anon";
GRANT ALL ON TABLE "public"."customers" TO "authenticated";
GRANT ALL ON TABLE "public"."customers" TO "service_role";



GRANT ALL ON TABLE "public"."live_sessions" TO "anon";
GRANT ALL ON TABLE "public"."live_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."live_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."organization_memberships" TO "anon";
GRANT ALL ON TABLE "public"."organization_memberships" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_memberships" TO "service_role";



GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";



GRANT ALL ON TABLE "public"."prices" TO "anon";
GRANT ALL ON TABLE "public"."prices" TO "authenticated";
GRANT ALL ON TABLE "public"."prices" TO "service_role";



GRANT ALL ON TABLE "public"."products" TO "anon";
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."subscriptions" TO "service_role";



GRANT ALL ON TABLE "public"."team_budget_limits" TO "anon";
GRANT ALL ON TABLE "public"."team_budget_limits" TO "authenticated";
GRANT ALL ON TABLE "public"."team_budget_limits" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
